# Ubuntu 22.04 环境配置文件
# 大语言模型演示项目环境变量

# Python环境配置
PYTHONPATH="${PYTHONPATH}:$(pwd)"
PYTHONUNBUFFERED=1

# Streamlit配置
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=0.0.0.0
STREAMLIT_SERVER_HEADLESS=true
STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# 模型缓存配置
HF_HOME=~/.cache/huggingface/
TRANSFORMERS_CACHE=~/.cache/huggingface/transformers/
HF_DATASETS_CACHE=~/.cache/huggingface/datasets/

# PyTorch配置
TORCH_HOME=~/.cache/torch/

# 网络配置（如果需要代理）
# HTTP_PROXY=http://your-proxy:port
# HTTPS_PROXY=https://your-proxy:port
# NO_PROXY=localhost,127.0.0.1

# 日志级别
LOG_LEVEL=INFO

# 禁用一些警告
PYTHONWARNINGS=ignore::UserWarning
TOKENIZERS_PARALLELISM=false

# 字体配置（解决中文显示问题）
MPLBACKEND=Agg
MATPLOTLIB_FONT_PATH=/usr/share/fonts/truetype/dejavu:/usr/share/fonts/truetype/liberation:/usr/share/fonts/truetype/noto
FONTCONFIG_PATH=/etc/fonts

# CUDA配置（如果有GPU）
CUDA_VISIBLE_DEVICES=0

# 内存优化
OMP_NUM_THREADS=4
MKL_NUM_THREADS=4
