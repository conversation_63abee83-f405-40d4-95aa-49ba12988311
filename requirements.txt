altair==5.4.0
attrs==25.1.0
blinker==1.9.0
cachetools==5.5.1
certifi==2024.12.14
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
filelock==3.17.0
fsspec==2024.12.0
gitdb==4.0.12
GitPython==3.1.44
huggingface-hub==0.28.1
idna==3.10
Jinja2==3.1.5
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
mpmath==1.3.0
narwhals==1.24.1
networkx==3.4.2
numpy==2.2.2
packaging==24.2
pandas==2.2.3
pillow==11.1.0
protobuf==5.29.3
pyarrow==19.0.0
pydeck==0.9.1
Pygments==2.19.1
pyobjc==11.0
pyobjc-core==11.0
pyobjc-framework-Accessibility==11.0
pyobjc-framework-Accounts==11.0
pyobjc-framework-AddressBook==11.0
pyobjc-framework-AdServices==11.0
pyobjc-framework-AdSupport==11.0
pyobjc-framework-AppleScriptKit==11.0
pyobjc-framework-AppleScriptObjC==11.0
pyobjc-framework-ApplicationServices==11.0
pyobjc-framework-AppTrackingTransparency==11.0
pyobjc-framework-AudioVideoBridging==11.0
pyobjc-framework-AuthenticationServices==11.0
pyobjc-framework-AutomaticAssessmentConfiguration==11.0
pyobjc-framework-Automator==11.0
pyobjc-framework-AVFoundation==11.0
pyobjc-framework-AVKit==11.0
pyobjc-framework-AVRouting==11.0
pyobjc-framework-BackgroundAssets==11.0
pyobjc-framework-BrowserEngineKit==11.0
pyobjc-framework-BusinessChat==11.0
pyobjc-framework-CalendarStore==11.0
pyobjc-framework-CallKit==11.0
pyobjc-framework-Carbon==11.0
pyobjc-framework-CFNetwork==11.0
pyobjc-framework-Cinematic==11.0
pyobjc-framework-ClassKit==11.0
pyobjc-framework-CloudKit==11.0
pyobjc-framework-Cocoa==11.0
pyobjc-framework-Collaboration==11.0
pyobjc-framework-ColorSync==11.0
pyobjc-framework-Contacts==11.0
pyobjc-framework-ContactsUI==11.0
pyobjc-framework-CoreAudio==11.0
pyobjc-framework-CoreAudioKit==11.0
pyobjc-framework-CoreBluetooth==11.0
pyobjc-framework-CoreData==11.0
pyobjc-framework-CoreHaptics==11.0
pyobjc-framework-CoreLocation==11.0
pyobjc-framework-CoreMedia==11.0
pyobjc-framework-CoreMediaIO==11.0
pyobjc-framework-CoreMIDI==11.0
pyobjc-framework-CoreML==11.0
pyobjc-framework-CoreMotion==11.0
pyobjc-framework-CoreServices==11.0
pyobjc-framework-CoreSpotlight==11.0
pyobjc-framework-CoreText==11.0
pyobjc-framework-CoreWLAN==11.0
pyobjc-framework-CryptoTokenKit==11.0
pyobjc-framework-DataDetection==11.0
pyobjc-framework-DeviceCheck==11.0
pyobjc-framework-DeviceDiscoveryExtension==11.0
pyobjc-framework-DictionaryServices==11.0
pyobjc-framework-DiscRecording==11.0
pyobjc-framework-DiscRecordingUI==11.0
pyobjc-framework-DiskArbitration==11.0
pyobjc-framework-DVDPlayback==11.0
pyobjc-framework-EventKit==11.0
pyobjc-framework-ExceptionHandling==11.0
pyobjc-framework-ExecutionPolicy==11.0
pyobjc-framework-ExtensionKit==11.0
pyobjc-framework-ExternalAccessory==11.0
pyobjc-framework-FileProvider==11.0
pyobjc-framework-FileProviderUI==11.0
pyobjc-framework-FinderSync==11.0
pyobjc-framework-FSEvents==11.0
pyobjc-framework-GameCenter==11.0
pyobjc-framework-GameController==11.0
pyobjc-framework-GameKit==11.0
pyobjc-framework-GameplayKit==11.0
pyobjc-framework-HealthKit==11.0
pyobjc-framework-ImageCaptureCore==11.0
pyobjc-framework-InputMethodKit==11.0
pyobjc-framework-InstallerPlugins==11.0
pyobjc-framework-InstantMessage==11.0
pyobjc-framework-Intents==11.0
pyobjc-framework-IntentsUI==11.0
pyobjc-framework-IOBluetooth==11.0
pyobjc-framework-IOBluetoothUI==11.0
pyobjc-framework-IOSurface==11.0
pyobjc-framework-iTunesLibrary==11.0
pyobjc-framework-KernelManagement==11.0
pyobjc-framework-LatentSemanticMapping==11.0
pyobjc-framework-LaunchServices==11.0
pyobjc-framework-libdispatch==11.0
pyobjc-framework-libxpc==11.0
pyobjc-framework-LinkPresentation==11.0
pyobjc-framework-LocalAuthentication==11.0
pyobjc-framework-LocalAuthenticationEmbeddedUI==11.0
pyobjc-framework-MailKit==11.0
pyobjc-framework-MapKit==11.0
pyobjc-framework-MediaAccessibility==11.0
pyobjc-framework-MediaExtension==11.0
pyobjc-framework-MediaLibrary==11.0
pyobjc-framework-MediaPlayer==11.0
pyobjc-framework-MediaToolbox==11.0
pyobjc-framework-Metal==11.0
pyobjc-framework-MetalFX==11.0
pyobjc-framework-MetalKit==11.0
pyobjc-framework-MetalPerformanceShaders==11.0
pyobjc-framework-MetalPerformanceShadersGraph==11.0
pyobjc-framework-MetricKit==11.0
pyobjc-framework-MLCompute==11.0
pyobjc-framework-ModelIO==11.0
pyobjc-framework-MultipeerConnectivity==11.0
pyobjc-framework-NaturalLanguage==11.0
pyobjc-framework-NetFS==11.0
pyobjc-framework-Network==11.0
pyobjc-framework-NetworkExtension==11.0
pyobjc-framework-NotificationCenter==11.0
pyobjc-framework-OpenDirectory==11.0
pyobjc-framework-OSAKit==11.0
pyobjc-framework-OSLog==11.0
pyobjc-framework-PassKit==11.0
pyobjc-framework-PencilKit==11.0
pyobjc-framework-PHASE==11.0
pyobjc-framework-Photos==11.0
pyobjc-framework-PhotosUI==11.0
pyobjc-framework-PreferencePanes==11.0
pyobjc-framework-PushKit==11.0
pyobjc-framework-Quartz==11.0
pyobjc-framework-QuickLookThumbnailing==11.0
pyobjc-framework-ReplayKit==11.0
pyobjc-framework-SafariServices==11.0
pyobjc-framework-SafetyKit==11.0
pyobjc-framework-SceneKit==11.0
pyobjc-framework-ScreenCaptureKit==11.0
pyobjc-framework-ScreenSaver==11.0
pyobjc-framework-ScreenTime==11.0
pyobjc-framework-ScriptingBridge==11.0
pyobjc-framework-SearchKit==11.0
pyobjc-framework-Security==11.0
pyobjc-framework-SecurityFoundation==11.0
pyobjc-framework-SecurityInterface==11.0
pyobjc-framework-SensitiveContentAnalysis==11.0
pyobjc-framework-ServiceManagement==11.0
pyobjc-framework-SharedWithYou==11.0
pyobjc-framework-SharedWithYouCore==11.0
pyobjc-framework-ShazamKit==11.0
pyobjc-framework-Social==11.0
pyobjc-framework-SoundAnalysis==11.0
pyobjc-framework-Speech==11.0
pyobjc-framework-SpriteKit==11.0
pyobjc-framework-StoreKit==11.0
pyobjc-framework-Symbols==11.0
pyobjc-framework-SyncServices==11.0
pyobjc-framework-SystemConfiguration==11.0
pyobjc-framework-SystemExtensions==11.0
pyobjc-framework-ThreadNetwork==11.0
pyobjc-framework-UniformTypeIdentifiers==11.0
pyobjc-framework-UserNotifications==11.0
pyobjc-framework-UserNotificationsUI==11.0
pyobjc-framework-VideoSubscriberAccount==11.0
pyobjc-framework-VideoToolbox==11.0
pyobjc-framework-Virtualization==11.0
pyobjc-framework-Vision==11.0
pyobjc-framework-WebKit==11.0
pyreadline==2.1
python-dateutil==2.9.0.post0
pytz==2024.2
PyYAML==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.27.1
rich==13.9.4
rpds-py==0.22.3
safetensors==0.5.2
six==1.17.0
smmap==5.0.2
streamlit==1.41.1
sympy==1.13.1
tenacity==9.0.0
tokenizers==0.21.0
toml==0.10.2
torch==2.6.0
tornado==6.4.2
tqdm==4.67.1
transformers==4.48.1
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
